import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * ProfileRequiredRoute component - Only accessible when authenticated AND profile is complete
 * Redirects to login if user is not authenticated
 * Redirects to user-details if user is authenticated but profile is incomplete
 */
const ProfileRequiredRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuthStore();
  const location = useLocation();

  // First check if user is authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Then check if profile is complete
  const isProfileComplete = currentUser?.is_profile_set === true;
  
  if (!isProfileComplete) {
    // Don't redirect if already on user-details page to prevent infinite loops
    if (location.pathname === '/user-details') {
      return children;
    }
    return <Navigate to="/user-details" replace />;
  }

  return children;
};

export default ProfileRequiredRoute;
