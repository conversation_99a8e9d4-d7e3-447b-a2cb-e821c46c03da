import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import SideNavBar from '../components/layout/SideNavBar';
import DashboardMainContent from '../components/layout/DashboardMainContent';
import { courseService } from '../services/courseService';
import { toast } from 'react-toastify';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState('home');
  const [courses, setCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Fetch courses on component mount
  useEffect(() => {
    const fetchCourses = async () => {
      if (!isAuthenticated) return;
      
      try {
        setIsLoading(true);
        const response = await courseService.getCourses();
        if (response && response.data) {
          // Transform API response to match expected format
          const formattedCourses = response.data.map(course => ({
            id: course.id,
            title: course.title,
            description: course.description,
            originalPrice: course.originalPrice,
            offerPrice: course.offerPrice,
            image: course.displayPicture,
            tags: course.tags,
            // Add any additional fields needed for display
            price: `₹${course.offerPrice}`, // Format price with currency
            duration: '4 months' // Default duration, update if available in API
          }));
          setCourses(formattedCourses);
        }
      } catch (error) {
        console.error('Error fetching courses:', error);
        toast.error('Failed to load courses. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, [isAuthenticated]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      setActiveTab('home');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
    // Add more navigation logic for future tabs
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseClick = (courseId) => {
    navigate(`/topics/${courseId}`);
  };



  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab={activeTab}
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <DashboardMainContent
        activeTab={activeTab}
        courses={courses}
        isLoading={isLoading}
        onCourseClick={handleCourseClick}
      />
    </div>
  );
};

export default Dashboard;
