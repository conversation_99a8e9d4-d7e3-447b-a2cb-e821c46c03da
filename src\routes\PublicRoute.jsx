import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PublicRoute component - Only accessible when NOT authenticated
 * Redirects to appropriate page based on authentication and profile completion status
 */
const PublicRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuthStore();

  if (!isAuthenticated) {
    return children;
  }

  // User is authenticated, check profile completion status
  const isProfileComplete = currentUser?.is_profile_set === true;

  if (isProfileComplete) {
    return <Navigate to="/dashboard" replace />;
  } else {
    return <Navigate to="/user-details" replace />;
  }
};

export default PublicRoute;
