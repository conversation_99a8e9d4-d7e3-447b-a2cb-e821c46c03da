import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AuthService from '../services/authService.js';

// Initialize auth service
const authService = new AuthService();

// Hardcoded user credentials for demo
const USERS = [
  { id: 'user1', user: 'user1', email: '<EMAIL>', password: 'pass1' },
  { id: 'user2', user: 'user2', email: '<EMAIL>', password: 'pass2' }
];

export const useAuthStore = create(
  persist(
    (set) => ({
      currentUser: null,
      isAuthenticated: false,

      login: async (email, password, recaptchaToken) => {
        try {
          // Use the API service for signin
          const response = await authService.signin(email, password, recaptchaToken);

          console.log('🔍 Login Response Debug:', response);

          if (response && response.success && response.data?.statusCode === 200) {
            // Extract user data from response.data.data
            const apiData = response.data.data;
            const userData = {
              id: apiData?.userId,
              email: apiData?.userEmailId || email,
              token: apiData?.token,
              is_profile_set: apiData?.is_profile_set,
              lastLogoutTime: apiData?.lastLogoutTime,
              ...apiData
            };

            console.log('✅ Login successful, setting user data:', userData);
            console.log('🔍 is_profile_set from API:', apiData?.is_profile_set, typeof apiData?.is_profile_set);

            set({
              currentUser: userData,
              isAuthenticated: true
            });

            // Return success with profile status for navigation decision
            return {
              success: true,
              is_profile_set: apiData?.is_profile_set
            };
          }

          console.log('❌ Login failed - invalid response structure');
          return { success: false };
        } catch (error) {
          console.error('Login failed:', error);

          // Fallback to hardcoded users for demo purposes
          const user = USERS.find(
            (u) => u.email.toLowerCase() === email.toLowerCase() && u.password === password
          );
          if (user) {
            console.log('🔄 Using fallback hardcoded user');
            set({ currentUser: user, isAuthenticated: true });
            return { success: true, is_profile_set: true }; // Assume profile is set for demo users
          }

          return { success: false };
        }
      },

      thirdPartyLogin: async (firebaseUser) => {
        try {
          console.log('🔍 Firebase user data:', firebaseUser);

          // Import helper functions dynamically to avoid circular dependencies
          const { extractSocialLoginData, getDeviceType, getClientIP, getClientLocation } = await import('../utils/helpers.js');

          // Extract social login data from Firebase user
          const socialData = extractSocialLoginData(firebaseUser);
          console.log('🔍 Extracted social data:', socialData);

          // Get client information
          const device = getDeviceType();
          const ipAddress = await getClientIP();
          const location = await getClientLocation();

          // Call backend API for social login
          const response = await authService.socialLogin(
            socialData.email,
            socialData.providerName,
            socialData.socialUserId,
            device,
            ipAddress,
            location
          );

          console.log('🔍 Social Login API Response:', response);
          console.log('🔍 Response validation:', {
            hasResponse: !!response,
            hasSuccess: response?.success,
            hasData: !!response?.data,
            statusCode: response?.data?.statusCode,
            isProfileSet: response?.data?.data?.is_profile_set
          });

          if (response && response.success && response.data?.statusCode === 200) {
            console.log('✅ API call successful, processing response...');
            // Extract user data from response.data.data
            const apiData = response.data.data;
            console.log('🔍 API Data:', apiData);
            console.log('🔍 is_profile_set from API:', apiData?.is_profile_set, typeof apiData?.is_profile_set);

            const userData = {
              id: apiData?.userId,
              email: apiData?.userEmailId || socialData.email,
              token: apiData?.token,
              is_profile_set: apiData?.is_profile_set,
              // Keep Firebase user data for display purposes
              displayName: firebaseUser.displayName || firebaseUser.providerData?.[0]?.displayName,
              photoURL: firebaseUser.photoURL || firebaseUser.providerData?.[0]?.photoURL,
              providerData: firebaseUser.providerData,
              ...apiData
            };

            console.log('✅ Social login successful, setting user data:', userData);

            set({
              currentUser: userData,
              isAuthenticated: true
            });

            // Return user data with profile status for navigation decision
            const returnValue = {
              success: true,
              is_profile_set: apiData?.is_profile_set
            };
            console.log('🔍 Returning to LoginPage:', returnValue);
            return returnValue;
          }

          console.log('❌ Social login failed - invalid response structure');
          return { success: false };
        } catch (error) {
          console.error('❌ Social login API call failed:', error);

          // Fallback to Firebase-only authentication for development
          console.log('🔄 Using fallback Firebase-only authentication (API failed)');
          set({
            currentUser: {
              id: firebaseUser.uid,
              email: firebaseUser.email || firebaseUser.providerData?.[0]?.email,
              displayName: firebaseUser.displayName || firebaseUser.providerData?.[0]?.displayName,
              photoURL: firebaseUser.photoURL || firebaseUser.providerData?.[0]?.photoURL,
              providerData: firebaseUser.providerData,
              is_profile_set: true // Assume profile is set for Firebase-only fallback
            },
            isAuthenticated: true
          });
          return { success: true, is_profile_set: true };
        }
      },

      logout: async () => {
        try {
          console.log('🔄 Logging out user and clearing tokens');

          // Call backend logout API and clear tokens
          await authService.logout();

          // Clear local state
          set({ currentUser: null, isAuthenticated: false });

          console.log('✅ Logout completed successfully');
        } catch (error) {
          console.error('❌ Logout error:', error);

          // Even if API call fails, clear local state and tokens
          await authService.apiClient.setAuthToken(null);
          set({ currentUser: null, isAuthenticated: false });
        }
      },

      // Set authentication state for traditional signup auto-signin
      setAuthenticationState: (userData) => {
        console.log('🔐 Setting authentication state for traditional signup:', userData);
        set({
          currentUser: userData,
          isAuthenticated: true
        });
      },

      // Update profile completion status
      updateProfileStatus: (isProfileSet) => {
        console.log('🔄 Updating profile completion status:', isProfileSet);
        set((state) => ({
          currentUser: state.currentUser ? {
            ...state.currentUser,
            is_profile_set: isProfileSet
          } : null
        }));
      },

      // Get profile completion status
      isProfileComplete: () => {
        const state = useAuthStore.getState();
        return state.currentUser?.is_profile_set === true;
      }
    }),
    { name: 'auth-storage' }
  )
);
