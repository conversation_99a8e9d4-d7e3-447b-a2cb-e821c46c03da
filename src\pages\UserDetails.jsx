import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, useBlocker } from 'react-router-dom';
import AnimationBox from '../components/common/AnimationBox';
import Header from '../components/layout/Header';
import { NavigationBlockModal } from '../components/ui';
import { initializeServices } from '../services/index.js';
import { useToastContext } from '../contexts/ToastContext';
import { useAuthStore } from '../stores/authStore';
import { validateAuthState, monitorAuthState, clearAuthStorage } from '../utils/authValidation';
import '../styles/UserDetails.css';
import '../styles/App.css';

const UserDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess, showError } = useToastContext();
  const { currentUser, isAuthenticated, updateProfileStatus, logout } = useAuthStore();

  const isTraditionalSignup = !!location.state?.email;
  const email = location.state?.email || '';

  // State declarations
  const [formData, setFormData] = useState({
    university: '',
    branch: '',
    year: '3fa85f64-5717-4562-b3fc-2c963f66afd1',
    semester: 'f46bcb0a-2bb6-4a55-b1a8-1c1b9db5a490'
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);
  const [universities, setUniversities] = useState([
    { value: '', label: 'Please select' }
  ]);
  const [branches, setBranches] = useState([
    { value: '', label: 'Please select' }
  ]);
  // State for navigation blocking modal
  const [showNavigationModal, setShowNavigationModal] = useState(false);

  // Check if profile is already complete
  const isProfileComplete = currentUser?.is_profile_set === true;

  // Authentication monitoring and validation
  const authMonitorRef = useRef(null);

  useEffect(() => {
    console.log('🔍 UserDetails state:', {
      isProfileComplete,
      currentUser: currentUser ? { ...currentUser, is_profile_set: currentUser.is_profile_set } : null,
      isLoading,
      pathname: location.pathname
    });

    // Validate current authentication state
    const authState = { isAuthenticated, currentUser };
    const authValidation = validateAuthState(authState);

    console.log('🔍 UserDetails auth validation:', authValidation);

    // If authentication is invalid, redirect to login immediately
    if (!authValidation.isValid) {
      console.log('❌ Authentication invalid in UserDetails, redirecting to login:', authValidation.reason);
      clearAuthStorage(true);
      navigate('/login', { replace: true });
      return;
    }

    // If profile is already complete, redirect to dashboard
    if (isProfileComplete) {
      console.log('✅ Profile already complete, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
      return;
    }

  }, [isProfileComplete, currentUser, isAuthenticated, isLoading, location.pathname, navigate]);

  // Set up continuous authentication monitoring
  useEffect(() => {
    const cleanup = monitorAuthState((authState, reason) => {
      console.log('🔍 Auth state changed in UserDetails:', { authState, reason });

      // If authentication becomes invalid, redirect to login
      if (!authState || reason !== 'valid') {
        console.log('❌ Authentication became invalid, redirecting to login:', reason);
        clearAuthStorage(true);
        navigate('/login', { replace: true });
      }
    });

    authMonitorRef.current = cleanup;

    return () => {
      if (authMonitorRef.current) {
        authMonitorRef.current();
      }
    };
  }, [navigate]);

  // Block navigation away from this page if profile is incomplete
  const shouldBlockNavigation = !isProfileComplete && !isLoading;

  const blocker = useBlocker(
    shouldBlockNavigation ? ({ currentLocation, nextLocation }) => {
      console.log('🔍 Navigation blocker check:', {
        currentPath: currentLocation.pathname,
        nextPath: nextLocation.pathname,
        isProfileComplete,
        shouldBlockNavigation
      });

      // Allow navigation if profile is complete
      if (isProfileComplete) {
        console.log('✅ Profile complete, allowing navigation');
        return false;
      }

      // Allow navigation to login page (for logout)
      if (nextLocation.pathname === '/login') {
        console.log('✅ Allowing navigation to login page');
        return false;
      }

      // Allow staying on the same page
      if (currentLocation.pathname === nextLocation.pathname) {
        console.log('✅ Same page, allowing navigation');
        return false;
      }

      // Block all other navigation when profile is incomplete
      console.log('🚫 Blocking navigation - profile incomplete');
      return true;
    } : false
  );

  // Initialize services once (depends on signup flow info if you need it logged)
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);

        console.log('🔍 UserDetails Flow Detection:', {
          isTraditionalSignup,
          hasEmail: !!email,
          email: email || 'No email provided',
          locationState: location.state
        });
      } catch (error) {
        console.error('Failed to initialize services:', error);
      }
    };

    initServices();
  }, [isTraditionalSignup, email, location.state]);

  // Add beforeunload event listener to warn users about leaving
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (shouldBlockNavigation) {
        event.preventDefault();
        event.returnValue = 'You have not completed your profile setup. Are you sure you want to leave?';
        return event.returnValue;
      }
    };

    if (shouldBlockNavigation) {
      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, [shouldBlockNavigation]);

  // Handle blocked navigation with custom modal
  useEffect(() => {
    console.log('🔍 Blocker state changed:', blocker.state);
    if (blocker.state === "blocked") {
      console.log('🚫 Navigation blocked, showing modal');
      setShowNavigationModal(true);
    } else if (blocker.state === "unblocked") {
      console.log('✅ Navigation unblocked, hiding modal');
      setShowNavigationModal(false);
    }
  }, [blocker.state]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      console.log('🧹 UserDetails component unmounting, cleaning up auth monitoring');
      if (authMonitorRef.current) {
        authMonitorRef.current();
      }
    };
  }, []);

  // Handle modal actions
  const handleStayOnPage = () => {
    console.log('👤 User chose to stay on page');
    setShowNavigationModal(false);
    blocker.reset();
  };

  const handleLeaveWithoutSaving = () => {
    console.log('👤 User chose to leave without saving');
    setShowNavigationModal(false);

    // Clear authentication monitoring to prevent interference
    if (authMonitorRef.current) {
      authMonitorRef.current();
      authMonitorRef.current = null;
    }

    // Use replace to prevent returning to UserDetails via back button
    blocker.proceed();
  };

  const handleLogout = async () => {
    console.log('👤 User chose to log out');
    setShowNavigationModal(false);

    try {
      // Clear authentication monitoring first
      if (authMonitorRef.current) {
        authMonitorRef.current();
        authMonitorRef.current = null;
      }

      // Perform logout
      await logout();

      // Clear all authentication storage
      clearAuthStorage(true);

      // Use replace to prevent returning to UserDetails via back button
      navigate('/login', { replace: true });

    } catch (error) {
      console.error('❌ Logout error:', error);

      // Even if logout fails, clear storage and redirect
      clearAuthStorage(true);
      navigate('/login', { replace: true });
    }
  };

  // Fetch universities when authService becomes available
  useEffect(() => {
    const fetchUniversities = async () => {
      if (!authService) return;

      try {
        console.log('🏫 Fetching universities automatically...');
        const result = await authService.selectUniversities();

        console.log('🔍 API Response Structure:', {
          success: result.success,
          hasData: !!result.data,
          dataKeys: result.data ? Object.keys(result.data) : [],
          universitiesArray: result.data?.data ? result.data.data.length : 0
        });

        if (result.success && result.data && result.data.data) {
          const universityOptions = [
            { value: '', label: 'Please select' },
            ...result.data.data.map(university => ({
              value: university.id || university.universityId,
              label: university.name || university.universityName
            }))
          ];

          setUniversities(universityOptions);
          console.log('✅ Universities loaded successfully:', universityOptions.length - 1, 'universities');
        } else {
          console.error('❌ Failed to fetch universities:', result.error || 'No data received');
          showError('Failed to load universities. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error fetching universities:', error);
        showError('Failed to load universities. Please refresh the page.');
      }
    };

    fetchUniversities();
  }, [authService, showError]);

  // Fetch branches when authService becomes available
  useEffect(() => {
    const fetchBranches = async () => {
      if (!authService) return;

      try {
        console.log('📚 Fetching branches automatically...');
        const result = await authService.selectBranches();

        console.log('🔍 Branches API Response Structure:', {
          success: result.success,
          hasData: !!result.data,
          dataKeys: result.data ? Object.keys(result.data) : [],
          branchesArray: result.data?.data ? result.data.data.length : 0
        });

        if (result.success && result.data && result.data.data) {
          const branchOptions = [
            { value: '', label: 'Please select' },
            ...result.data.data.map(branch => ({
              value: branch.id || branch.branchId,
              label: branch.name || branch.branchName
            }))
          ];

          setBranches(branchOptions);
          console.log('✅ Branches loaded successfully:', branchOptions.length - 1, 'branches');
        } else {
          console.error('❌ Failed to fetch branches:', result.error || 'No data received');
          showError('Failed to load branches. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error fetching branches:', error);
        showError('Failed to load branches. Please refresh the page.');
      }
    };

    fetchBranches();
  }, [authService, showError]);

  const years = [
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd1', label: 'Year 1' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd2', label: 'Year 2' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd3', label: 'Year 3' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd4', label: 'Year 4' }
  ];

  const semesters = [
    { value: 'f46bcb0a-2bb6-4a55-b1a8-1c1b9db5a490', label: 'Semester 1' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc2', label: 'Semester 2' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.university) newErrors.university = 'Please select a university';
    if (!formData.branch) newErrors.branch = 'Please select a branch';
    if (!formData.year) newErrors.year = 'Please select a year';
    if (!formData.semester) newErrors.semester = 'Please select a semester';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (e) => {
    e.preventDefault();
    if (!validateForm() || !authService) return;

    setIsLoading(true);
    try {
      console.log('🔍 UserDetails Form Data:', {
        university: formData.university,
        branch: formData.branch,
        semester: formData.semester,
        formData
      });

      const result = await authService.setProfile(
        formData.branch,
        formData.semester,
        formData.university
      );

      if (result.success) {
        console.log('✅ Profile setup completed successfully');

        // Clear authentication monitoring since we're leaving the page
        if (authMonitorRef.current) {
          authMonitorRef.current();
          authMonitorRef.current = null;
        }

        // Update profile completion status in auth store
        updateProfileStatus(true);

        showSuccess('User details saved successfully! Your account is now complete.');

        // Use replace to prevent returning to UserDetails via back button
        navigate('/dashboard', { replace: true });
      } else {
        console.error('❌ Profile setup failed:', result.error);
        showError(result.error || 'Failed to save user details');
      }
    } catch (error) {
      console.error('❌ Signup error:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'An error occurred while saving user details';
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="user-details-container">
      <Header />
      <div className="user-details-content">
        <AnimationBox className="user-details-box">
          <h2 className="h3 user-details-title">Set Up Your Profile</h2>
          <form onSubmit={handleSave} className="user-details-form">
            {/* Branch Dropdown */}
            <div className="form-group">
              <label htmlFor="branch" className="body3 form-label">What branch are you studying:</label>
              <select
                id="branch"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                className={`dropdown-select ${errors.branch ? 'error' : ''}`}
              >
                {branches.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.branch && <div className="error-message body4">{errors.branch}</div>}
            </div>

            {/* Year Selection */}
            <div className="form-group">
              <label className="body3 form-label">What year are you in:</label>
              <div className="segmented-control">
                {years.map((option, index) => (
                  <button
                    key={option.value}
                    type="button"
                    className={`segmented-option ${formData.year === option.value ? 'selected' : ''} ${index === 0 ? 'first' : ''} ${index === years.length - 1 ? 'last' : ''}`}
                    onClick={() => handleInputChange('year', option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              {errors.year && <div className="error-message body4">{errors.year}</div>}
            </div>

            {/* Semester Selection */}
            <div className="form-group">
              <label className="body3 form-label">Which semester are you currently in:</label>
              <div className="segmented-control">
                {semesters.map((option, index) => (
                  <button
                    key={option.value}
                    type="button"
                    className={`segmented-option ${formData.semester === option.value ? 'selected' : ''} ${index === 0 ? 'first' : ''} ${index === semesters.length - 1 ? 'last' : ''}`}
                    onClick={() => handleInputChange('semester', option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              {errors.semester && <div className="error-message body4">{errors.semester}</div>}
            </div>

            {/* University Dropdown */}
            <div className="form-group">
              <label htmlFor="university" className="body3 form-label">What university are you in:</label>
              <select
                id="university"
                value={formData.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={`dropdown-select ${errors.university ? 'error' : ''}`}
              >
                {universities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.university && <div className="error-message body4">{errors.university}</div>}
            </div>

            {/* Save Button */}
            <div className="save-button-container">
              <button
                type="submit"
                className="save-button body3-bold"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Next'}
              </button>
            </div>
          </form>
        </AnimationBox>
      </div>

      {/* Navigation Block Modal */}
      <NavigationBlockModal
        isOpen={showNavigationModal}
        onStay={handleStayOnPage}
        onLeave={handleLeaveWithoutSaving}
        onLogout={handleLogout}
        showLogoutOption={true}
        title="Profile Setup Incomplete"
        message="You haven't completed your profile setup yet. Leaving this page will prevent you from accessing the application. You can continue setting up your profile, leave without saving, or log out to switch accounts."
      />
    </div>
  );
};

export default UserDetails;