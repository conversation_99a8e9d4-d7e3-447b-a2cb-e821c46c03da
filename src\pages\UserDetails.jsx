import { useState, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import AnimationBox from '../components/common/AnimationBox';
import Header from '../components/layout/Header';
import { initializeServices } from '../services/index.js';
import { useToastContext } from '../contexts/ToastContext';
import '../styles/UserDetails.css';
import '../styles/App.css';

const UserDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess, showError } = useToastContext();

  const isTraditionalSignup = !!location.state?.email;
  const email = location.state?.email || '';

  const [formData, setFormData] = useState({
    university: '',
    branch: '',
    year: '3fa85f64-5717-4562-b3fc-2c963f66afd1',
    semester: 'f46bcb0a-2bb6-4a55-b1a8-1c1b9db5a490'
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);
  const [universities, setUniversities] = useState([
    { value: '', label: 'Please select' }
  ]);
  const [branches, setBranches] = useState([
    { value: '', label: 'Please select' }
  ]);

  // Initialize services once (depends on signup flow info if you need it logged)
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);

        console.log('🔍 UserDetails Flow Detection:', {
          isTraditionalSignup,
          hasEmail: !!email,
          email: email || 'No email provided',
          locationState: location.state
        });
      } catch (error) {
        console.error('Failed to initialize services:', error);
      }
    };

    initServices();
  }, [isTraditionalSignup, email, location.state]);

  // Fetch universities when authService becomes available
  useEffect(() => {
    const fetchUniversities = async () => {
      if (!authService) return;

      try {
        console.log('🏫 Fetching universities automatically...');
        const result = await authService.selectUniversities();

        console.log('🔍 API Response Structure:', {
          success: result.success,
          hasData: !!result.data,
          dataKeys: result.data ? Object.keys(result.data) : [],
          universitiesArray: result.data?.data ? result.data.data.length : 0
        });

        if (result.success && result.data && result.data.data) {
          const universityOptions = [
            { value: '', label: 'Please select' },
            ...result.data.data.map(university => ({
              value: university.id || university.universityId,
              label: university.name || university.universityName
            }))
          ];

          setUniversities(universityOptions);
          console.log('✅ Universities loaded successfully:', universityOptions.length - 1, 'universities');
        } else {
          console.error('❌ Failed to fetch universities:', result.error || 'No data received');
          showError('Failed to load universities. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error fetching universities:', error);
        showError('Failed to load universities. Please refresh the page.');
      }
    };

    fetchUniversities();
  }, [authService, showError]);

  // Fetch branches when authService becomes available
  useEffect(() => {
    const fetchBranches = async () => {
      if (!authService) return;

      try {
        console.log('📚 Fetching branches automatically...');
        const result = await authService.selectBranches();

        console.log('🔍 Branches API Response Structure:', {
          success: result.success,
          hasData: !!result.data,
          dataKeys: result.data ? Object.keys(result.data) : [],
          branchesArray: result.data?.data ? result.data.data.length : 0
        });

        if (result.success && result.data && result.data.data) {
          const branchOptions = [
            { value: '', label: 'Please select' },
            ...result.data.data.map(branch => ({
              value: branch.id || branch.branchId,
              label: branch.name || branch.branchName
            }))
          ];

          setBranches(branchOptions);
          console.log('✅ Branches loaded successfully:', branchOptions.length - 1, 'branches');
        } else {
          console.error('❌ Failed to fetch branches:', result.error || 'No data received');
          showError('Failed to load branches. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error fetching branches:', error);
        showError('Failed to load branches. Please refresh the page.');
      }
    };

    fetchBranches();
  }, [authService, showError]);

  const years = [
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd1', label: 'Year 1' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd2', label: 'Year 2' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd3', label: 'Year 3' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afd4', label: 'Year 4' }
  ];

  const semesters = [
    { value: 'f46bcb0a-2bb6-4a55-b1a8-1c1b9db5a490', label: 'Semester 1' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc2', label: 'Semester 2' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.university) newErrors.university = 'Please select a university';
    if (!formData.branch) newErrors.branch = 'Please select a branch';
    if (!formData.year) newErrors.year = 'Please select a year';
    if (!formData.semester) newErrors.semester = 'Please select a semester';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (e) => {
    e.preventDefault();
    if (!validateForm() || !authService) return;

    setIsLoading(true);
    try {
      console.log('🔍 UserDetails Form Data:', {
        university: formData.university,
        branch: formData.branch,
        semester: formData.semester,
        formData
      });

      const result = await authService.setProfile(
        formData.branch,
        formData.semester,
        formData.university
      );

      if (result.success) {
        console.log('✅ Signup completed successfully');
        showSuccess('User details saved successfully! Your account is now complete.');
        navigate('/dashboard');
      } else {
        console.error('❌ Signup failed:', result.error);
        showError(result.error || 'Failed to save user details');
      }
    } catch (error) {
      console.error('❌ Signup error:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'An error occurred while saving user details';
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="user-details-container">
      <Header />
      <div className="user-details-content">
        <AnimationBox className="user-details-box">
          <h2 className="h3 user-details-title">Set Up Your Profile</h2>
          <form onSubmit={handleSave} className="user-details-form">
            {/* Branch Dropdown */}
            <div className="form-group">
              <label htmlFor="branch" className="body3 form-label">What branch are you studying:</label>
              <select
                id="branch"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                className={`dropdown-select ${errors.branch ? 'error' : ''}`}
              >
                {branches.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.branch && <div className="error-message body4">{errors.branch}</div>}
            </div>

            {/* Year Selection */}
            <div className="form-group">
              <label className="body3 form-label">What year are you in:</label>
              <div className="segmented-control">
                {years.map((option, index) => (
                  <button
                    key={option.value}
                    type="button"
                    className={`segmented-option ${formData.year === option.value ? 'selected' : ''} ${index === 0 ? 'first' : ''} ${index === years.length - 1 ? 'last' : ''}`}
                    onClick={() => handleInputChange('year', option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              {errors.year && <div className="error-message body4">{errors.year}</div>}
            </div>

            {/* Semester Selection */}
            <div className="form-group">
              <label className="body3 form-label">Which semester are you currently in:</label>
              <div className="segmented-control">
                {semesters.map((option, index) => (
                  <button
                    key={option.value}
                    type="button"
                    className={`segmented-option ${formData.semester === option.value ? 'selected' : ''} ${index === 0 ? 'first' : ''} ${index === semesters.length - 1 ? 'last' : ''}`}
                    onClick={() => handleInputChange('semester', option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              {errors.semester && <div className="error-message body4">{errors.semester}</div>}
            </div>

            {/* University Dropdown */}
            <div className="form-group">
              <label htmlFor="university" className="body3 form-label">What university are you in:</label>
              <select
                id="university"
                value={formData.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={`dropdown-select ${errors.university ? 'error' : ''}`}
              >
                {universities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.university && <div className="error-message body4">{errors.university}</div>}
            </div>

            {/* Save Button */}
            <div className="save-button-container">
              <button
                type="submit"
                className="save-button body3-bold"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Next'}
              </button>
            </div>
          </form>
        </AnimationBox>
      </div>
    </div>
  );
};

export default UserDetails;