/**
 * Authentication Validation Utilities
 * Provides comprehensive token and authentication state validation
 */

/**
 * Validate if a JWT token is expired
 * @param {string} token - JWT token to validate
 * @returns {boolean} True if token is valid (not expired), false otherwise
 */
export function isTokenValid(token) {
  if (!token || typeof token !== 'string') {
    return false;
  }

  try {
    // JWT tokens have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }

    // Decode the payload (second part)
    const payload = JSON.parse(atob(parts[1]));
    
    // Check if token has expiration time
    if (!payload.exp) {
      // If no expiration, consider it valid (some tokens don't expire)
      return true;
    }

    // Check if token is expired (exp is in seconds, Date.now() is in milliseconds)
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp > currentTime;
  } catch (error) {
    console.warn('Error validating token:', error);
    return false;
  }
}

/**
 * Validate authentication state comprehensively
 * @param {Object} authState - Authentication state from store
 * @returns {Object} Validation result with details
 */
export function validateAuthState(authState) {
  const result = {
    isValid: false,
    hasUser: false,
    hasToken: false,
    isTokenValid: false,
    isAuthenticated: false,
    reason: null
  };

  // Check if authenticated flag is set
  if (!authState?.isAuthenticated) {
    result.reason = 'Not authenticated';
    return result;
  }
  result.isAuthenticated = true;

  // Check if user exists
  if (!authState.currentUser) {
    result.reason = 'No user data';
    return result;
  }
  result.hasUser = true;

  // Check if token exists
  const token = authState.currentUser.token;
  if (!token) {
    result.reason = 'No authentication token';
    return result;
  }
  result.hasToken = true;

  // Validate token
  if (!isTokenValid(token)) {
    result.reason = 'Invalid or expired token';
    return result;
  }
  result.isTokenValid = true;

  // All checks passed
  result.isValid = true;
  result.reason = 'Valid authentication state';
  return result;
}

/**
 * Clear all authentication-related data from storage
 * @param {boolean} clearPersisted - Whether to clear persisted Zustand data
 */
export function clearAuthStorage(clearPersisted = true) {
  try {
    // Clear localStorage items
    const authStorageKey = 'auth-storage';
    if (clearPersisted && localStorage.getItem(authStorageKey)) {
      localStorage.removeItem(authStorageKey);
      console.log('🧹 Cleared persisted auth storage');
    }

    // Clear any other auth-related localStorage items
    const authKeys = Object.keys(localStorage).filter(key => 
      key.includes('auth') || key.includes('token') || key.includes('user')
    );
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🧹 Cleared localStorage key: ${key}`);
    });

    // Clear sessionStorage items
    const sessionAuthKeys = Object.keys(sessionStorage).filter(key => 
      key.includes('auth') || key.includes('token') || key.includes('user')
    );
    
    sessionAuthKeys.forEach(key => {
      sessionStorage.removeItem(key);
      console.log(`🧹 Cleared sessionStorage key: ${key}`);
    });

  } catch (error) {
    console.warn('Error clearing auth storage:', error);
  }
}

/**
 * Check if user should have access to UserDetails page
 * @param {Object} authState - Authentication state
 * @returns {Object} Access validation result
 */
export function validateUserDetailsAccess(authState) {
  const authValidation = validateAuthState(authState);
  
  const result = {
    hasAccess: false,
    shouldRedirectToLogin: false,
    shouldRedirectToDashboard: false,
    reason: null
  };

  // If not authenticated at all, redirect to login
  if (!authValidation.isValid) {
    result.shouldRedirectToLogin = true;
    result.reason = `Authentication invalid: ${authValidation.reason}`;
    return result;
  }

  // If authenticated and profile is complete, redirect to dashboard
  const isProfileComplete = authState.currentUser?.is_profile_set === true;
  if (isProfileComplete) {
    result.shouldRedirectToDashboard = true;
    result.reason = 'Profile already complete';
    return result;
  }

  // If authenticated but profile incomplete, allow access
  result.hasAccess = true;
  result.reason = 'Authenticated with incomplete profile';
  return result;
}

/**
 * Monitor authentication state changes and trigger callbacks
 * @param {Function} onAuthStateChange - Callback for auth state changes
 * @returns {Function} Cleanup function
 */
export function monitorAuthState(onAuthStateChange) {
  let lastAuthState = null;

  const checkAuthState = () => {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (!authStorage) {
        if (lastAuthState !== null) {
          lastAuthState = null;
          onAuthStateChange(null, 'storage_cleared');
        }
        return;
      }

      const { state } = JSON.parse(authStorage);
      const currentAuthState = {
        isAuthenticated: state?.isAuthenticated || false,
        currentUser: state?.currentUser || null
      };

      // Check if auth state changed
      const stateChanged = JSON.stringify(lastAuthState) !== JSON.stringify(currentAuthState);
      if (stateChanged) {
        const validation = validateAuthState(currentAuthState);
        lastAuthState = currentAuthState;
        onAuthStateChange(currentAuthState, validation.isValid ? 'valid' : validation.reason);
      }
    } catch (error) {
      console.warn('Error monitoring auth state:', error);
      if (lastAuthState !== null) {
        lastAuthState = null;
        onAuthStateChange(null, 'monitoring_error');
      }
    }
  };

  // Initial check
  checkAuthState();

  // Set up periodic monitoring
  const interval = setInterval(checkAuthState, 1000); // Check every second

  // Listen for storage changes from other tabs
  const handleStorageChange = (event) => {
    if (event.key === 'auth-storage') {
      checkAuthState();
    }
  };

  window.addEventListener('storage', handleStorageChange);

  // Return cleanup function
  return () => {
    clearInterval(interval);
    window.removeEventListener('storage', handleStorageChange);
  };
}
