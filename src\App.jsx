import React, { useEffect, useRef } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import "./styles/App.css";
import {
  LoginPage,
  SignupPage,
  OTPVerificationPage,
  CreatePasswordPage,
  ReadingPage,
  TermsPage,
  PrivacyPage,
  UserDetails,
  Dashboard,
  Profile,
  TopicsPage
} from "./pages";
import { ErrorBoundary } from "./components/common";
import { PrivateRoute, PublicRoute } from "./routes";
import { useAuthStore } from "./stores";
import { useErrorHandler } from "./hooks";
import { ToastProvider } from "./contexts/ToastContext";

// Main App content component
export function AppContent({ config }) {
  const { currentUser, isAuthenticated } = useAuthStore();
  const { error, handleError, clearError, addError } = useErrorHandler();

  // Log the config for debugging
  useEffect(() => {
    console.log('App config:', config);
  }, [config]);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  return (
    <ToastProvider>
      <ErrorBoundary
        message={null} // Let ErrorBoundary use the actual error message
        onError={(error, errorInfo) => {
          console.error("App Error Boundary:", error, errorInfo);
          addError({
            id: Date.now(),
            message: error.message || "An error occurred. Please refresh the page.",
            type: "error",
          });
        }}
      >
        <Routes>
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/privacy"
            element={
              <PublicRoute>
                <PrivacyPage/>
              </PublicRoute>
            }
          />
          <Route
          path="/terms"
          element={
            <PublicRoute>
              <TermsPage/>
            </PublicRoute>
          }
          />
          <Route
            path="/signup"
            element={
              <PublicRoute>
                <SignupPage />
              </PublicRoute>
            }
          />
          <Route
            path="/otp-verification"
            element={
              <PublicRoute>
                <OTPVerificationPage />
              </PublicRoute>
            }
          />
          <Route
            path="/set-password"
            element={
              <PublicRoute>
                <CreatePasswordPage />
              </PublicRoute>
            }
          />
          <Route
            path="/forgot-password"
            element={
              <PublicRoute>
                <SignupPage mode="forgot-password" />
              </PublicRoute>
            }
          />
          <Route
            path="/user-details"
            element={
              <PrivateRoute>
                <UserDetails/>
              </PrivateRoute>
            }
          />
          <Route
            path="/dashboard"
            element={
              <PrivateRoute>
                <Dashboard/>
              </PrivateRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <PrivateRoute>
                <Profile/>
              </PrivateRoute>
            }
          />
          <Route
            path="/topics/:courseId"
            element={
              <PrivateRoute>
                <TopicsPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/reading"
            element={
              <PrivateRoute>
                <ReadingPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/"
            element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />}
          />
        </Routes>
      </ErrorBoundary>
    </ToastProvider>
  );
}

// Wrapper component for production use
function AppWithRouter({ config }) {
  return (
    <Router>
      <AppContent config={config} />
    </Router>
  );
}

export default AppWithRouter;
