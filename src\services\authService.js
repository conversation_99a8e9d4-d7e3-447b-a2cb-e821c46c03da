/**
 * Authentication Service - Business logic layer for authentication
 */
import { apiClient, API_ENDPOINTS, withErrorHandling } from '../api/index.js';
import { encryptPassword } from '../utils/crypto.js';
import { generateDeviceIdWithTimestamp } from '../utils/deviceHelper.js';

class AuthService {
  constructor() {
    this.apiClient = apiClient;
  }

  /**
   * Send email verification for signup or forgot password
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @param {string} purpose - Purpose: 'sign_up' or 'forgot_password'
   * @returns {Promise} Service response
   */
  async sendEmailVerification(email, recaptchaToken, purpose = 'sign_up') {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.emailVerification, {
          email,
          purpose,
          recaptchaToken
        });
        return response;
      },
      { operation: 'sendEmailVerification', email, purpose }
    );
  }

  /**
   * Validate OTP code with token and purpose
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @param {string} token - Verification token
   * @param {string} purpose - Purpose of OTP validation (default: 'sign_up')
   * @returns {Promise} Service response
   */
  async validateOTP(email, otp, token, purpose = 'sign_up') {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.validateOTP, {
          email,
          otp,
          token,
          purpose
        });
        return response;
      },
      { operation: 'validateOTP', email, purpose }
    );
  }

  /**
   * Set password for user account
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @param {string} token - Token from OTP validation response (optional for backward compatibility)
   * @returns {Promise} Service response
   */
  async setPassword(email, password, confirmPassword, token = null) {
    // Validate passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match'
      };
    }

    return await withErrorHandling(
      async () => {
        // Encrypt password and use for both fields since they should be identical
        const encryptedPassword = await encryptPassword(password);

        const payload = {
          email,
          password: encryptedPassword,
          confirmPassword: encryptedPassword // Use same encrypted value
        };

        // Add token to payload if provided
        if (token) {
          payload.token = token;
        }

        const response = await this.apiClient.post(API_ENDPOINTS.auth.setPassword, payload);

        // Extract and store JWT token from response for traditional signup flow
        console.log('🔍 setPassword response structure:', {
          hasData: !!response.data,
          hasToken: !!response.data?.token,
          tokenLength: response.data?.token?.length || 0,
          responseKeys: response.data ? Object.keys(response.data) : []
        });

        if (response.data?.token) {
          console.log('🔐 JWT token received from setPassword, storing for subsequent API calls');
          await this.apiClient.setAuthToken(response.data.token);

          // Set authentication state in store for traditional signup flow
          // Import auth store dynamically to avoid circular dependencies
          const { useAuthStore } = await import('../stores/authStore.js');
          const setAuthenticationState = useAuthStore.getState().setAuthenticationState;

          // Create user data from the response
          const userData = {
            id: response.data?.userId,
            email: response.data?.userEmailId || email,
            token: response.data.token,
            ...response.data
          };

          console.log('🔐 Setting authentication state after setPassword:', userData);
          setAuthenticationState(userData);
        } else {
          console.warn('⚠️ No JWT token found in setPassword response - traditional signup flow may not work properly');
        }

        return response;
      },
      { operation: 'setPassword', email, hasToken: !!token }
    );
  }

  /**
   * Complete user signup with user details (set profile)
   * @param {string} branchId - Branch UUID
   * @param {string} semesterId - Semester UUID
   * @param {string} universityId - University UUID
   * @returns {Promise} Service response
   */
  async setProfile(branchId, semesterId, universityId) {
    return await withErrorHandling(
      async () => {
        const payload = {
          branchId,
          semesterId,
          universityId
        };

        console.log('📤 Set Profile API Call:', {
          endpoint: API_ENDPOINTS.auth.signup,
          payload,
          operation: 'setProfile'
        });

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signup, payload);

        console.log('📥 Set Profile API Response:', response);

        return response;
      },
      { operation: 'setProfile' }
    );
  }

  /**
   * Complete user signup with user details
   * @param {string} branchId - Branch UUID
   * @param {string} semesterId - Semester UUID
   * @param {string} universityId - University UUID
   * @returns {Promise} Service response
   */
  async signup(branchId, semesterId, universityId) {
    return await withErrorHandling(
      async () => {
        const payload = {
          branchId,
          semesterId,
          universityId
        };

        console.log('📤 Signup API Call:', {
          endpoint: API_ENDPOINTS.auth.signup,
          payload,
          operation: 'signup'
        });

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signup, payload);

        console.log('📥 Signup API Response:', response);

        return response;
      },
      { operation: 'signup' }
    );
  }

  /**
   * Create user account (legacy method - kept for backward compatibility)
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} Service response
   */
  async createAccount(email, password, confirmPassword) {
    // Validate passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match'
      };
    }

    return await withErrorHandling(
      async () => {
        // Encrypt password and use for both fields since they should be identical
        const encryptedPassword = await encryptPassword(password);

        const response = await this.apiClient.post(API_ENDPOINTS.auth.createAccount, {
          email,
          password: encryptedPassword,
          confirmPassword: encryptedPassword // Use same encrypted value
        });
        return response;
      },
      { operation: 'createAccount', email }
    );
  }

  /**
   * Sign in user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async signin(email, password, recaptchaToken) {
    const result = await withErrorHandling(
      async () => {
        // Encrypt password before sending to API
        const encryptedPassword = await encryptPassword(password);

        // Generate device ID for this authentication attempt
        const deviceId = generateDeviceIdWithTimestamp();

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signin, {
          email,
          password: encryptedPassword,
          recaptchaToken,
          device: deviceId,
          portalType:"WEBSITE"
        });

        // Store auth token if provided (check response.data.token for API response structure)
        if (response.data?.token) {
          await this.apiClient.setAuthToken(response.data.token);
        }

        return response;
      },
      { operation: 'signin', email }
    );

    return result;
  }

  async selectUniversities() {
    const result = await withErrorHandling(async () => {
      const response = await this.apiClient.get(API_ENDPOINTS.user.selectUniversities);
      return response;
    });
    return result;
  }
  async selectBranches(){
    const result = await withErrorHandling(async() =>{
      const response = await this.apiClient.get(API_ENDPOINTS.user.selectBranches);
      return response;
    })
    return result;
  }

  /**
   * Login user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async login(email, password, recaptchaToken) {
    const result = await withErrorHandling(
      async () => {
        // Encrypt password before sending to API
        const encryptedPassword = await encryptPassword(password);

        // Generate device ID for this authentication attempt
        const deviceId = generateDeviceIdWithTimestamp();

        const response = await this.apiClient.post(API_ENDPOINTS.auth.login, {
          email,
          password: encryptedPassword,
          recaptchaToken,
          device: deviceId
        });

        // Store auth token if provided
        if (response.token) {
          await this.apiClient.setAuthToken(response.token);
        }

        return response;
      },
      { operation: 'login', email }
    );

    return result;
  }

  /**
   * Social login with OAuth providers
   * @param {string} email - User email address
   * @param {string} providerName - OAuth provider name (google, microsoft, facebook)
   * @param {string} socialUserId - Social provider's unique user ID
   * @param {string} deviceType - Device type (default: "web") - for backward compatibility
   * @param {string} ipAddress - Client IP address (optional)
   * @param {string} location - User location (optional)
   * @param {string} fcmToken - Firebase messaging token (optional)
   * @returns {Promise} Service response
   */
  async socialLogin(email, providerName, socialUserId, deviceType = "web", ipAddress = null, location = null, fcmToken = null) {
    const result = await withErrorHandling(
      async () => {
        // Generate device ID for this authentication attempt
        const deviceId = generateDeviceIdWithTimestamp();

        const payload = {
          email,
          providerName,
          socialUserId,
          device: deviceId,
          portalType: "WEBSITE"
        };

        // Add optional fields if provided
        if (ipAddress) payload.ipAddress = ipAddress;
        if (location) payload.location = location;
        if (fcmToken) payload.fcmToken = fcmToken;

        console.log('🔐 Social Login Payload:', payload);

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signin, payload);

        // Store auth token if provided
        if (response.data?.token) {
          await this.apiClient.setAuthToken(response.data.token);
        }

        return response;
      },
      { operation: 'socialLogin', email, providerName }
    );

    return result;
  }

  /**
   * Logout user
   * @returns {Promise} Service response
   */
  async logout() {
    const result = await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.logout);
        return response;
      },
      { operation: 'logout' }
    );

    // Clear auth token regardless of API call result
    await this.apiClient.setAuthToken(null);

    // If API call failed but we still want to clear local state
    if (!result.success) {
      return {
        success: true,
        data: { message: 'Logged out locally' }
      };
    }

    return result;
  }





  /**
   * Reset password with token
   * @param {string} token - Reset token
   * @param {string} email - User email address
   * @param {string} password - New password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} Service response
   */
  async resetPassword(token, email, password, confirmPassword) {
    // Validate passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match'
      };
    }

    return await withErrorHandling(
      async () => {
        // Encrypt password and use for both fields since they should be identical
        const encryptedPassword = await encryptPassword(password);

        // Generate device ID for this authentication attempt
        const deviceId = generateDeviceIdWithTimestamp();

        const payload = {
          token,
          email,
          password: encryptedPassword,
          confirmPassword: encryptedPassword, // Use same encrypted value
          device: deviceId
        };

        console.log('📤 Reset Password API Call:', {
          endpoint: API_ENDPOINTS.auth.resetPassword,
          payload: { ...payload, password: '[ENCRYPTED]', confirmPassword: '[ENCRYPTED]' },
          operation: 'resetPassword'
        });

        const response = await this.apiClient.post(API_ENDPOINTS.auth.resetPassword, payload);

        console.log('📥 Reset Password API Response:', response);

        return response;
      },
      { operation: 'resetPassword', email }
    );
  }

  /**
   * Resend email verification
   * @param {string} email - User email address
   * @param {string} purpose - Purpose: 'sign_up' or 'forgot_password'
   * @returns {Promise} Service response
   */
  async resendEmailVerification(email, purpose = 'sign_up') {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.resendVerification, {
          email,
          purpose
        });
        return response;
      },
      { operation: 'resendEmailVerification', email, purpose }
    );
  }
}

export default AuthService;
