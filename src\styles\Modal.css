/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 480px;
  padding: 2rem;
  animation: slideUp 0.3s ease-out;
  border: 1px solid var(--border-color, #e0e0e0);
}

.modal-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #333);
  text-align: center;
}

.modal-content > div {
  margin-bottom: 1.5rem;
  color: var(--text-color, #666);
  line-height: 1.5;
  text-align: center;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.modal-actions button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-width: 120px;
}

/* Cancel Button */
.cancel-button {
  background-color: transparent;
  color: var(--text-color, #666);
  border-color: var(--border-color, #e0e0e0);
}

.cancel-button:hover {
  background-color: #f8f9fa;
  border-color: #d0d7de;
}

.cancel-button:focus {
  outline: 2px solid #0969da;
  outline-offset: 2px;
}

/* Confirm Button */
.confirm-button {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.confirm-button:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.confirm-button:focus {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

/* Primary Button Variant */
.primary-button {
  background-color: #0969da;
  color: white;
  border-color: #0969da;
}

.primary-button:hover {
  background-color: #0860ca;
  border-color: #0860ca;
}

.primary-button:focus {
  outline: 2px solid #0969da;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modal-content {
    width: 95%;
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .modal-actions button {
    width: 100%;
  }
}

/* Animation for modal exit */
.modal-overlay.exiting {
  animation: fadeOut 0.2s ease-out;
}

.modal-overlay.exiting .modal-content {
  animation: slideDown 0.2s ease-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

/* Accessibility improvements */
.modal-overlay:focus {
  outline: none;
}

.modal-content:focus {
  outline: none;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}
